using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;

namespace Requests.Application.Tickets.CreatePublicTicket;

internal sealed class CreatePublicTicketCommandValidator : AbstractValidator<CreatePublicTicketCommand>
{
    public CreatePublicTicketCommandValidator(IRequestsDbContext context)
    {
        RuleFor(x => x.SubjectId)
            .NotEmpty().WithMessage("Konu seçimi zorunludur.")
            .MustAsync(async (subjectId, ct) =>
                await context.TicketSubjects.AnyAsync(s => s.Id == subjectId, ct))
            .WithMessage("Seçilen konu bulunamadı.");

        RuleFor(x => x.CustomerId)
            .NotEmpty().WithMessage("Müşteri ID'si zorunludur.");

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Başlık boş olamaz.")
            .MaximumLength(200).WithMessage("Başlık en fazla 200 karakter olabilir.");

        RuleFor(x => x.Description)
            .MaximumLength(2000).WithMessage("Açıklama en fazla 2000 karakter olabilir.");
    }
}
