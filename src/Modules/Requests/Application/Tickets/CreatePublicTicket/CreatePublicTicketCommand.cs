using MediatR;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Tickets.CreatePublicTicket;

public record CreatePublicTicketCommand : IRequest<Result<Guid>>
{
    public Guid SubjectId { get; init; }
    public required Guid CustomerId { get; init; } // Artık zorunlu - doğrulanmış müşteri ID'si
    public required string Title { get; init; }
    public string? Description { get; init; }
    public PriorityEnum Priority { get; init; } = PriorityEnum.Medium;
    public string? Country { get; init; }
    public string? State { get; init; }
    public string? City { get; init; }
    public string? Province { get; init; }
    public string? Detail { get; init; }
}
