using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;
using Shared.Contracts;
using Shared.Domain;
using Shared.Utilities;

namespace Requests.Application.Tickets.CreatePublicTicket;

internal sealed class CreatePublicTicketCommandHandler(
    IRequestsDbContext dbContext,
    ITicketHistoryService historyService,
    AppSettings appSettings,
    ISharedCustomerService customerService
) : IRequestHandler<CreatePublicTicketCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(
        CreatePublicTicketCommand request,
        CancellationToken cancellationToken)
    {
        var customer = await customerService.GetCustomerAsync(request.CustomerId);
        if (!customer.IsSuccess || customer.Value == null)
        {
            return Result.Failure<Guid>("Kullanıcı bulunamadı.");
        }
        if (customer.Value.IsPhoneVerified == false)
        {
            return Result.Failure<Guid>("Kullanıcı telefonu doğrulanmamış.");
        }
        var ticketId = Guid.NewGuid();
        var ticketCode = CodeGenerator.ProcessFormat(appSettings.TicketCodeFormat, "Ticket");
        Guid? statusId = null;
        var subject = await dbContext.TicketSubjects.FindAsync([request.SubjectId], cancellationToken);
        if (subject != null && subject.FlowId.HasValue)
        {
            var flow = await dbContext.Flows
                .Include(x => x.Nodes)
                .FirstOrDefaultAsync(f => f.Id == subject.FlowId.Value, cancellationToken);
            statusId = flow?.Nodes.FirstOrDefault(n => n.NodeType == NodeType.Start)?.Id;
        }
        var systemUserId = Guid.Empty;
        var ticket = new Ticket
        {
            Id = ticketId,
            Code = ticketCode,
            TicketType = TicketType.Ticket,
            SubjectId = request.SubjectId,
            CustomerId = request.CustomerId,
            Title = request.Title,
            Description = request.Description ?? "",
            ReporterUserId = systemUserId,
            Priority = request.Priority,
            StatusId = statusId,
            Country = request.Country,
            State = request.State,
            City = request.City,
            Province = request.Province,
            Detail = request.Detail,
            Watchlist = [],
            Tags = []
        };
        dbContext.Tickets.Add(ticket);
        await historyService.TrackTicketCreatedAsync(ticket.Id, ticket, cancellationToken);
        await dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success(ticketId);
    }
}
