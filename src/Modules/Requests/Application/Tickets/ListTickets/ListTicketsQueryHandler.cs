using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;
using Shared.Application.Extensions;
using Shared.Contracts;

namespace Requests.Application.Tickets.ListTickets;

public class ListTicketsQueryHandler(
    IRequestsDbContext dbContext,
    ISharedUserService userService,
    ISharedCustomerService customerService,
    IWorkContext workContext
) : IRequestHandler<ListTicketsQuery, PagedResult<TicketListItemDto>>
{
    private readonly IRequestsDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;
    private readonly ISharedCustomerService _customerService = customerService;
    private readonly IWorkContext _workContext = workContext;

    public async Task<PagedResult<TicketListItemDto>> Handle(ListTicketsQuery request, CancellationToken cancellationToken)
    {
        var user = await _workContext.GetUserAsync();
        var query = _dbContext.Tickets
            .Include(t => t.Subject)
            .Include(t => t.TicketDepartment)
            .Include(t => t.Status)
            .Include(t => t.TicketFiles)
            .AsQueryable();
        if (!_workContext.HasRole("Admin") && !_workContext.HasRole("AGENT"))
        {
            if (_workContext.HasRole("LEADER"))
            {
                var userDto = await _userService.GetUserAsync(user.Id);
                query = query.Where(x => x.TicketDepartment.Any(y => userDto.Value.DepartmentIds.Contains(y.Id)));
            }
            else
            {
                query = query.Where(x =>
                x.UserId == user.Id ||
                x.ReporterUserId == user.Id ||
                //x.Watchlist.Any(y => y == user.Id) ||
                x.InsertUserId == user.Id);
            }
        }
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.Trim().ToLower();
            query = query
                .Where(t => t.Title.ToLower().Contains(searchTerm) ||
                            t.Description.ToLower().Contains(searchTerm));
        }
        if (!string.IsNullOrWhiteSpace(request.Title))
        {
            var title = request.Title.Trim().ToLower();
            query = query.Where(t => t.Title.ToLower().Contains(title));
        }
        if (!string.IsNullOrWhiteSpace(request.Code))
        {
            var code = request.Code.Trim().ToLower();
            query = query.Where(t => t.Code.ToLower().Contains(code));
        }
        if (request.CustomerId.HasValue)
        {
            query = query.Where(t => t.CustomerId == request.CustomerId.Value);
        }
        if (request.CallId.HasValue)
        {
            query = query.Where(t => t.CallId == request.CallId.Value);
        }
        if (request.ChatId.HasValue)
        {
            query = query.Where(t => t.ChatId == request.ChatId.Value);
        }
        if (request.UserIds?.Length > 0)
        {
            query = query.Where(t => request.UserIds.Contains(t.UserId.Value));
        }
        if (request.WatchUserIds?.Length > 0)
        {
            query = query.Where(t => request.WatchUserIds.Any(x => request.WatchUserIds.Contains(x)));
        }
        if (request.Type.HasValue)
        {
            query = query.Where(t => t.TicketType == request.Type);
        }
        if (request.Priority.HasValue)
        {
            query = query.Where(t => t.Priority == request.Priority.Value);
        }
        if (request.StatusId.HasValue)
        {
            query = query.Where(t => t.StatusId == request.StatusId.Value);
        }
        if (request.StatusIds?.Length > 0)
        {
            query = query.Where(t => request.StatusIds.Contains(t.StatusId.Value));
        }
        if (request.FlowId.HasValue)
        {
            query = query.Where(t => t.Subject.FlowId == request.FlowId.Value);
        }
        if (request.FlowIds?.Length > 0)
        {
            query = query.Where(t => request.FlowIds.Contains(t.Subject.FlowId.Value));
        }
        if (request.SubjectId.HasValue)
        {
            query = query.Where(t => t.SubjectId == request.SubjectId.Value);
        }
        if (request.SubjectIds?.Length > 0)
        {
            query = query.Where(t => request.SubjectIds.Contains(t.SubjectId));
        }
        if (request.StartDate.HasValue)
        {
            query = query.Where(t => t.InsertDate >= request.StartDate.Value);
        }
        if (request.EndDate.HasValue)
        {
            query = query.Where(t => t.InsertDate <= request.EndDate.Value);
        }
        if (request.DepartmentIds?.Length > 0)
        {
            query = query.Where(t => t.TicketDepartment.Any(td => request.DepartmentIds.Contains(td.DepartmentId)));
        }
        if (request.NodeTypes?.Length > 0)
        {
            query = query.Where(t => t.StatusId.HasValue && request.NodeTypes.Contains(t.Status.NodeType));
        }
        if (request.TopTicketId.HasValue)
        {
            query = query.Where(t => t.TopTicketId == request.TopTicketId);
        }
        if (!string.IsNullOrWhiteSpace(request.Country))
        {
            var country = request.Country.Trim().ToLower();
            query = query.Where(t => t.Country.ToLower().Contains(country));
        }
        if (!string.IsNullOrWhiteSpace(request.State))
        {
            var state = request.State.Trim().ToLower();
            query = query.Where(t => t.State.ToLower().Contains(state));
        }
        if (!string.IsNullOrWhiteSpace(request.City))
        {
            var city = request.City.Trim().ToLower();
            query = query.Where(t => t.City.ToLower().Contains(city));
        }
        if (!string.IsNullOrWhiteSpace(request.Province))
        {
            var province = request.Province.Trim().ToLower();
            query = query.Where(t => t.Province.ToLower().Contains(province));
        }
        if (!string.IsNullOrWhiteSpace(request.Detail))
        {
            var detail = request.Detail.Trim().ToLower();
            query = query.Where(t => t.Detail.ToLower().Contains(detail));
        }

        // Apply generic filters - Choose one:
        // Option 1: Simple generic filtering
        //query = query.ApplyFilters(request);

        // Option 2: Advanced attribute-based filtering
        // query = query.ApplyAdvancedFilters(request);

        var totalCount = await _dbContext.Tickets.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);
        var tickets = await query
            .ApplySorting(request.SortProperty, request.SortType)
            .ApplyPaging(request.PageNumber, request.PageSize)
            .ToListAsync(cancellationToken);
        var ticketIds = tickets.Select(t => t.Id).ToList();
        var commentCounts = await _dbContext.TicketComments
            .Where(c => ticketIds.Contains(c.TicketId))
            .GroupBy(c => c.TicketId)
            .Select(g => new { TicketId = g.Key, Count = g.Count() })
            .ToDictionaryAsync(g => g.TicketId, g => g.Count, cancellationToken);
        var userIds = tickets.Where(t => t.UserId.HasValue)
            .Select(t => t.UserId!.Value)
            .Concat(tickets.Select(t => t.ReporterUserId))
            .Distinct()
            .ToList();
        var customerIds = tickets.Where(x => x.CustomerId.HasValue).Select(t => t.CustomerId ?? Guid.Empty).Distinct().ToList();
        var users = new Dictionary<Guid, SharedUserDto>();
        var customers = new Dictionary<Guid, SharedCustomerDto>();
        var userResult = await _userService.GetUsersByIdsAsync(userIds);
        users = userResult.ToDictionary(u => u.Id, u => u);
        var customerResult = await _customerService.GetCustomerByIdsAsync(customerIds);
        customers = customerResult.Value.ToDictionary(c => c.Id, c => c);
        var notificationWay = await _customerService.GetNotificationWaysAsync();
        var result = tickets.Select(t => new TicketListItemDto
        {
            Id = t.Id,
            Code = t.Code,
            TicketType = t.TicketType,
            SubjectId = t.SubjectId,
            SubjectName = t.Subject.Name,
            SlaTime = t.Subject.Sla,
            CustomerId = t.CustomerId,
            CustomerName = t.CustomerId.HasValue && customers.TryGetValue(t.CustomerId.Value, out var customer) ? $"{customer.Name} {customer.Surname}" : null,
            CallId = t.CallId,
            ChatId = t.ChatId,
            Title = t.Title,
            Description = t.Description,
            NotificationWayId = t.NotificationWayId,
            NotificationWay = notificationWay.Value.FirstOrDefault(x => x.Id == t.NotificationWayId)?.Name,
            UserId = t.UserId,
            UserName = t.UserId.HasValue && users.TryGetValue(t.UserId.Value, out var user) ? $"{user.Name} {user.Surname}" : null,
            ReporterUserId = t.ReporterUserId,
            ReporterUserName = users.TryGetValue(t.ReporterUserId, out var reporter) ? $"{reporter.Name} {reporter.Surname}" : null,
            Priority = t.Priority,
            StatusId = t.StatusId,
            StatusName = t.Status?.Name,
            Status = new NodeDto { Id = t.Status.Id, Name = t.Status?.Name, Type = t.Status?.NodeType.ToString() },
            StatusType = t.Status?.NodeType,
            EndDate = t.EndDate,
            InsertDate = t.InsertDate,
            Country = t.Country,
            State = t.State,
            City = t.City,
            Province = t.Province,
            Detail = t.Detail,
            CommentCount = commentCounts.TryGetValue(t.Id, out var count) ? count : 0,
            HasAttachment = t.TicketFiles.Any(),
            AttributeData = t.AttributeData,
            Watchlist = t.Watchlist,
            Departments = [.. t.TicketDepartment.Select(td => new DepartmentDto
            {
                DepartmentId = td.DepartmentId,
                Name = td.DepartmentName
            })],
            SubTicketCount = t.SubTickets?.Count ?? 0,
            SubDoneTicketCount = t.SubTickets?.Count(x => x.Status?.NodeType == NodeType.End) ?? 0
        }).ToList();
        var pagedResult = PagedResult<TicketListItemDto>.Success(result);
        pagedResult.PageNumber = request.PageNumber;
        pagedResult.PageSize = request.PageSize;
        pagedResult.Count = totalCount;
        pagedResult.FilteredCount = filteredCount;
        return pagedResult;
    }
}
