﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Customers.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class CustomersData020 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsPhoneVerified",
                schema: "Customers",
                table: "Customer",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "PhoneVerificationCode",
                schema: "Customers",
                table: "Customer",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PhoneVerificationCodeExpiresAt",
                schema: "Customers",
                table: "Customer",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PhoneVerifiedAt",
                schema: "Customers",
                table: "Customer",
                type: "datetime2",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsPhoneVerified",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "PhoneVerificationCode",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "PhoneVerificationCodeExpiresAt",
                schema: "Customers",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "PhoneVerifiedAt",
                schema: "Customers",
                table: "Customer");
        }
    }
}
