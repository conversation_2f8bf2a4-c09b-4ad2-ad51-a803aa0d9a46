using Customers.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Customers.Infrastructure.Data.Configurations;

internal sealed class PhoneVerificationConfiguration : IEntityTypeConfiguration<PhoneVerification>
{
    public void Configure(EntityTypeBuilder<PhoneVerification> builder)
    {
        builder.HasKey(x => x.Id);

        builder.Property(x => x.Phone)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(x => x.PhonePrefix)
            .IsRequired()
            .HasMaxLength(5);

        builder.Property(x => x.VerificationCode)
            .IsRequired()
            .HasMaxLength(10);

        builder.Property(x => x.ExpiresAt)
            .IsRequired();

        builder.Property(x => x.IsVerified)
            .IsRequired();

        builder.Property(x => x.IsUsed)
            .IsRequired();

        builder.Property(x => x.CreatedAt)
            .IsRequired();

        builder.Property(x => x.VerifiedAt);

        // Index for phone lookup
        builder.HasIndex(x => new { x.Phone, x.PhonePrefix })
            .HasDatabaseName("IX_PhoneVerification_Phone_PhonePrefix");

        // Index for verification code lookup
        builder.HasIndex(x => x.VerificationCode)
            .HasDatabaseName("IX_PhoneVerification_VerificationCode");
    }
}
