using System.Reflection;
using Customers.Application.Abstractions;
using Customers.Domain;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Infrastructure.Data;

namespace Customers.Infrastructure.Data;

public class CustomersDbContext(
    DbContextOptions<CustomersDbContext> options,
    IWorkContext workContext,
    IEventBus eventBus
) : BaseDbContext(options, workContext, eventBus), ICustomersDbContext
{
    public DbSet<Customer> Customers { get; set; }
    public DbSet<TempCustomer> TempCustomers { get; set; }
    public DbSet<Contact> Contacts { get; set; }
    public DbSet<AdvisorHistory> AdvisorHistory { get; set; }
    public DbSet<Address> Address { get; set; }
    public DbSet<CustomerSource> CustomerSource { get; set; }
    public DbSet<Classification> Classification { get; set; }
    public DbSet<CustomerClassification> CustomerClassification { get; set; }
    public DbSet<Profession> Profession { get; set; }
    public DbSet<Sector> Sector { get; set; }
    public DbSet<NotificationWay> NotificationWays { get; set; }
    public DbSet<PhoneVerification> PhoneVerifications { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.HasDefaultSchema("Customers");
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
