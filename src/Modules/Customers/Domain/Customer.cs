using Customers.Domain.Events;
using Shared.Domain;

namespace Customers.Domain;

public class Customer : AuditableEntity
{
    private Customer(
        string name,
        string? surname,
        string? email,
        string phone,
        string phonePrefix,
        CustomerType type) : base()
    {
        Name = name;
        Surname = surname;
        Email = email;
        Phone = phone;
        PhonePrefix = phonePrefix;
        Type = type;
        _contacts = [];
        _advisorHistory = [];
    }
    public Guid Id { get; set; }
    public string Name { get; private set; }
    public string? Surname { get; private set; }
    public string? Email { get; private set; }
    public string Phone { get; private set; }
    public string? PhonePrefix { get; set; }
    public string? TaxOffice { get; set; }
    public string? TaxNumber { get; set; }
    public string? IdentificationNumber { get; set; }
    public string? Country { get; set; }
    public string? MainLanguage { get; set; }
    public string? AvailableLanguage { get; set; }
    public string? Description { get; set; }
    public string? MailBcc { get; set; }
    public bool HideInformation { get; set; } = false;
    public CustomerType Type { get; set; }
    public CustomerKind? Kind { get; set; }
    public CustomerStatus? Status { get; set; }
    public Guid? ProfessionId { get; set; }
    public Profession? Profession { get; set; }
    public Guid? SectorId { get; set; }
    public Sector? Sector { get; set; }
    public Guid? CustomerSourceId { get; set; }
    public CustomerSource? CustomerSource { get; set; }
    public Guid? NotificationWayId { get; set; }
    public NotificationWay? NotificationWay { get; set; }
    public List<CustomerClassification>? CustomerClassifications { get; set; } = [];
    public Guid? TopCustomerId { get; set; }
    public Customer? TopCustomer { get; set; }
    public List<Customer>? SubCustomers { get; set; }
    public List<Guid>? AdvisorIds { get; set; } = [];
    public Dictionary<string, string>? AttributeData { get; set; }
    public bool IsDeleted { get; private set; }
    public List<Address>? Addresses { get; set; }

    // Phone verification fields
    public string? PhoneVerificationCode { get; private set; }
    public DateTime? PhoneVerificationCodeExpiresAt { get; private set; }
    public bool IsPhoneVerified { get; private set; } = false;
    public DateTime? PhoneVerifiedAt { get; private set; }

    private readonly List<Contact> _contacts = [];
    private readonly List<AdvisorHistory> _advisorHistory = [];

    public IReadOnlyCollection<Contact> Contacts => _contacts.AsReadOnly();
    public IReadOnlyCollection<AdvisorHistory> AdvisorHistory => _advisorHistory.AsReadOnly();

    public static Customer Create(
        string name,
        string? surname,
        string? email,
        string phone,
        string phonePrefix,
        CustomerType type)
    {
        var customer = new Customer(name, surname, email, phone, phonePrefix, type);
        customer.Raise(new CustomerCreatedEvent(customer.Id));
        return customer;
    }

    public void UpdateBasicInfo(
        string name,
        string? surname,
        string? email,
        string phone,
        string phonePrefix,
        CustomerType type)
    {
        Name = name;
        Surname = surname;
        Email = email;
        Phone = phone;
        PhonePrefix = phonePrefix;
        Type = type;

        Raise(new CustomerUpdatedEvent(Id));
    }

    public void AssignAdvisor(Guid advisorId)
    {
        AdvisorIds ??= [];
        if (AdvisorIds.Contains(advisorId))
        {
            return;
        }
        _advisorHistory.Add(new AdvisorHistory(Id, Phone, advisorId));
        AdvisorIds.Add(advisorId);
        Raise(new CustomerAdvisorAssignedEvent(Id, advisorId));
    }

    public void AssignAdvisors(List<Guid> advisorIds)
    {
        if (advisorIds == null || !advisorIds.Any())
        {
            return;
        }

        foreach (var advisorId in advisorIds)
        {
            AssignAdvisor(advisorId);
        }
    }

    public void AddContact(Contact contact)
    {
        _contacts.Add(contact);
        Raise(new CustomerContactAddedEvent(Id, contact.Id));
    }

    public void Delete()
    {
        if (IsDeleted)
        {
            return;
        }
        IsDeleted = true;
        Raise(new CustomerDeletedEvent(Id));
    }

    public void AddClassification(Guid classificationId)
    {
        CustomerClassifications ??= [];

        if (!CustomerClassifications.Any(cc => cc.ClassificationId == classificationId))
        {
            CustomerClassifications.Add(new CustomerClassification
            {
                CustomerId = Id,
                ClassificationId = classificationId
            });
        }
    }

    public void RemoveClassification(Guid classificationId)
    {
        if (CustomerClassifications == null)
        {
            return;
        }

        var classification = CustomerClassifications.FirstOrDefault(cc => cc.ClassificationId == classificationId);
        if (classification != null)
        {
            CustomerClassifications.Remove(classification);
        }
    }

    public void UpdateClassifications(List<Guid> classificationIds)
    {
        CustomerClassifications ??= [];

        // Mevcut sınıflandırmaları temizle
        var classificationsToRemove = CustomerClassifications.ToList();
        foreach (var classification in classificationsToRemove)
        {
            CustomerClassifications.Remove(classification);
        }

        // Yeni sınıflandırmaları ekle
        foreach (var classificationId in classificationIds)
        {
            AddClassification(classificationId);
        }
    }

    public void SetPhoneVerificationCode(string verificationCode, int expirationMinutes = 5)
    {
        PhoneVerificationCode = verificationCode;
        PhoneVerificationCodeExpiresAt = DateTime.UtcNow.AddMinutes(expirationMinutes);
    }

    public bool IsPhoneVerificationCodeValid(string code)
    {
        if (string.IsNullOrWhiteSpace(PhoneVerificationCode) ||
            string.IsNullOrWhiteSpace(code) ||
            PhoneVerificationCodeExpiresAt == null)
        {
            return false;
        }

        return PhoneVerificationCode == code && DateTime.UtcNow <= PhoneVerificationCodeExpiresAt;
    }

    public void VerifyPhone()
    {
        IsPhoneVerified = true;
        PhoneVerifiedAt = DateTime.UtcNow;
        PhoneVerificationCode = null;
        PhoneVerificationCodeExpiresAt = null;
    }

    public void ClearPhoneVerificationCode()
    {
        PhoneVerificationCode = null;
        PhoneVerificationCodeExpiresAt = null;
    }
}
