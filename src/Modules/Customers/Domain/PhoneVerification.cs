using Shared.Domain;

namespace Customers.Domain;

public class PhoneVerification : BaseEntity
{
    public Guid Id { get; set; }
    public string Phone { get; set; }
    public string PhonePrefix { get; set; }
    public string VerificationCode { get; set; }
    public DateTime ExpiresAt { get; set; }
    public bool IsVerified { get; set; }
    public bool IsUsed { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? VerifiedAt { get; set; }

    public static PhoneVerification Create(string phone, string phonePrefix, string verificationCode, int expirationMinutes = 5)
    {
        return new PhoneVerification
        {
            Id = Guid.NewGuid(),
            Phone = phone,
            PhonePrefix = phonePrefix,
            VerificationCode = verificationCode,
            ExpiresAt = DateTime.UtcNow.AddMinutes(expirationMinutes),
            IsVerified = false,
            IsUsed = false,
            CreatedAt = DateTime.UtcNow
        };
    }

    public bool IsExpired => DateTime.UtcNow > ExpiresAt;

    public void MarkAsVerified()
    {
        IsVerified = true;
        VerifiedAt = DateTime.UtcNow;
    }

    public void MarkAsUsed()
    {
        IsUsed = true;
    }
}
