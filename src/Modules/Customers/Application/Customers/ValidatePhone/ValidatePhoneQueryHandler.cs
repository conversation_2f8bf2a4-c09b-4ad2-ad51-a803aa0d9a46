using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.Validation;
using Shared.Infrastructure.External.Sms;

namespace Customers.Application.Customers.ValidatePhone;

internal sealed class SendVerificationCodeCommandHandler(
    ICustomersDbContext context,
    ISmsManager smsManager
) : IRequestHandler<SendVerificationCodeCommand, Result<SendVerificationCodeResponse>>
{
    public async Task<Result<SendVerificationCodeResponse>> Handle(
        SendVerificationCodeCommand request,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Phone))
        {
            return Result.Success(new SendVerificationCodeResponse(
                Success: false,
                Message: "Telefon numarası boş olamaz.",
                VerificationId: null
            ));
        }

        if (!PhoneNormalizer.TryNormalize(request.Phone, out var normalized, out var prefix))
        {
            return Result.Success(new SendVerificationCodeResponse(
                Success: false,
                Message: "Geçersiz telefon numarası formatı.",
                VerificationId: null
            ));
        }

        var (finalNormalized, finalPrefix) = PhoneNormalizer.ApplyRegionalRules(normalized, prefix);

        // Önceki doğrulama kodlarını pasif yap
        var existingVerifications = await context.PhoneVerifications
            .Where(pv => pv.Phone == finalNormalized && pv.PhonePrefix == finalPrefix && !pv.IsUsed)
            .ToListAsync(cancellationToken);

        foreach (var verification in existingVerifications)
        {
            verification.MarkAsUsed();
        }

        // 6 haneli rastgele kod oluştur
        var verificationCode = GenerateVerificationCode();

        // Yeni doğrulama kaydı oluştur
        var phoneVerification = PhoneVerification.Create(finalNormalized, finalPrefix, verificationCode);
        context.PhoneVerifications.Add(phoneVerification);

        try
        {
            // SMS gönder
            var fullPhone = $"{finalPrefix}{finalNormalized}";
            var message = $"Doğrulama kodunuz: {verificationCode}. Bu kod 5 dakika geçerlidir.";
            await smsManager.SendSmsAsync(fullPhone, message);

            await context.SaveChangesAsync(cancellationToken);

            return Result.Success(new SendVerificationCodeResponse(
                Success: true,
                Message: "Doğrulama kodu gönderildi.",
                VerificationId: phoneVerification.Id.ToString()
            ));
        }
        catch (Exception ex)
        {
            return Result.Success(new SendVerificationCodeResponse(
                Success: false,
                Message: "SMS gönderilirken hata oluştu.",
                VerificationId: null
            ));
        }
    }

    private static string GenerateVerificationCode()
    {
        var random = new Random();
        return random.Next(100000, 999999).ToString();
    }
}
