using FluentValidation;

namespace Customers.Application.Customers.VerifyPhone;

internal sealed class VerifyPhoneCommandValidator : AbstractValidator<VerifyPhoneCommand>
{
    public VerifyPhoneCommandValidator()
    {
        RuleFor(x => x.CustomerId)
            .NotEmpty().WithMessage("Müşteri ID'si boş olamaz.");

        RuleFor(x => x.VerificationCode)
            .NotEmpty().WithMessage("Doğrulama kodu boş olamaz.")
            .Length(6).WithMessage("Doğrulama kodu 6 haneli olmalıdır.")
            .Matches(@"^\d{6}$").WithMessage("Doğrulama kodu sadece rakamlardan oluşmalıdır.");
    }
}
