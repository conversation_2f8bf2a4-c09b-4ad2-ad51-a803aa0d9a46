using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Customers.VerifyPhone;

internal sealed class VerifyPhoneCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<VerifyPhoneCommand, Result<VerifyPhoneResponse>>
{
    public async Task<Result<VerifyPhoneResponse>> Handle(
        VerifyPhoneCommand request,
        CancellationToken cancellationToken)
    {
        var customer = await context.Customers
            .FirstOrDefaultAsync(c => c.Id == request.CustomerId, cancellationToken);
        if (customer == null)
        {
            return Result.Success(new VerifyPhoneResponse(
                Success: false,
                Message: "Müşteri bulunamadı.",
                CustomerId: null
            ));
        }
        if (!customer.IsPhoneVerificationCodeValid(request.VerificationCode))
        {
            return Result.Success(new VerifyPhoneResponse(
                Success: false,
                Message: "Geçersiz veya süresi dolmuş doğrulama kodu.",
                CustomerId: null
            ));
        }
        customer.VerifyPhone();
        await context.SaveChangesAsync(cancellationToken);
        return Result.Success(new VerifyPhoneResponse(
            Success: true,
            Message: "Telefon numarası başarıyla doğrulandı.",
            CustomerId: customer.Id
        ));
    }
}
