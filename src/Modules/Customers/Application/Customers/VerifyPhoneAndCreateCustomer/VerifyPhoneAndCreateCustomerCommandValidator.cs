using FluentValidation;

namespace Customers.Application.Customers.VerifyPhoneAndCreateCustomer;

internal sealed class VerifyPhoneAndCreateCustomerCommandValidator : AbstractValidator<VerifyPhoneAndCreateCustomerCommand>
{
    public VerifyPhoneAndCreateCustomerCommandValidator()
    {
        RuleFor(x => x.Phone)
            .NotEmpty().WithMessage("Telefon numarası boş olamaz.");

        RuleFor(x => x.VerificationCode)
            .NotEmpty().WithMessage("Doğrulama kodu boş olamaz.")
            .Length(6).WithMessage("Doğrulama kodu 6 haneli olmalıdır.")
            .Matches(@"^\d{6}$").WithMessage("Doğrulama kodu sadece rakamlardan oluşmalıdır.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Ad boş olamaz.")
            .MaximumLength(100).WithMessage("Ad en fazla 100 karakter olabilir.");

        RuleFor(x => x.Surname)
            .MaximumLength(100).WithMessage("Soyad en fazla 100 karakter olabilir.")
            .When(x => !string.IsNullOrWhiteSpace(x.Surname));

        RuleFor(x => x.Email)
            .EmailAddress().WithMessage("Geçerli bir e-posta adresi giriniz.")
            .When(x => !string.IsNullOrWhiteSpace(x.Email));
    }
}
