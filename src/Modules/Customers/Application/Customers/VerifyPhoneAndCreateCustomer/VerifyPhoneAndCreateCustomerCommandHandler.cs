using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.Validation;

namespace Customers.Application.Customers.VerifyPhoneAndCreateCustomer;

internal sealed class VerifyPhoneAndCreateCustomerCommandHandler(
    ICustomersDbContext context
) : IRequestHandler<VerifyPhoneAndCreateCustomerCommand, Result<VerifyPhoneAndCreateCustomerResponse>>
{
    public async Task<Result<VerifyPhoneAndCreateCustomerResponse>> Handle(
        VerifyPhoneAndCreateCustomerCommand request,
        CancellationToken cancellationToken)
    {
        // Telefon numarasını normalize et
        if (!PhoneNormalizer.TryNormalize(request.Phone, out var normalized, out var prefix))
        {
            return Result.Success(new VerifyPhoneAndCreateCustomerResponse(
                Success: false,
                Message: "Geçersiz telefon numarası formatı.",
                CustomerId: null
            ));
        }

        var (finalNormalized, finalPrefix) = PhoneNormalizer.ApplyRegionalRules(normalized, prefix);

        // Doğrulama kodunu kontrol et
        var verification = await context.PhoneVerifications
            .Where(pv => pv.Phone == finalNormalized &&
                        pv.PhonePrefix == finalPrefix &&
                        pv.VerificationCode == request.VerificationCode &&
                        !pv.IsUsed)
            .OrderByDescending(pv => pv.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);

        if (verification == null)
        {
            return Result.Success(new VerifyPhoneAndCreateCustomerResponse(
                Success: false,
                Message: "Geçersiz doğrulama kodu.",
                CustomerId: null
            ));
        }

        if (verification.IsExpired)
        {
            return Result.Success(new VerifyPhoneAndCreateCustomerResponse(
                Success: false,
                Message: "Doğrulama kodu süresi dolmuş.",
                CustomerId: null
            ));
        }

        // Aynı telefon numarasıyla müşteri var mı kontrol et
        var existingCustomer = await context.Customers
            .FirstOrDefaultAsync(c => c.Phone == finalNormalized && c.PhonePrefix == finalPrefix, cancellationToken);

        if (existingCustomer != null)
        {
            // Doğrulama kodunu kullanıldı olarak işaretle
            verification.MarkAsVerified();
            verification.MarkAsUsed();
            await context.SaveChangesAsync(cancellationToken);

            return Result.Success(new VerifyPhoneAndCreateCustomerResponse(
                Success: true,
                Message: "Telefon numarası doğrulandı. Mevcut müşteri bulundu.",
                CustomerId: existingCustomer.Id
            ));
        }

        // Yeni müşteri oluştur
        string email = !string.IsNullOrWhiteSpace(request.Email)
            ? request.Email.Trim()
            : GenerateRandomEmail();

        var customer = Customer.Create(
            request.Name,
            request.Surname,
            email,
            finalNormalized,
            finalPrefix,
            CustomerType.Individual);

        context.Customers.Add(customer);

        // Doğrulama kodunu kullanıldı olarak işaretle
        verification.MarkAsVerified();
        verification.MarkAsUsed();

        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(new VerifyPhoneAndCreateCustomerResponse(
            Success: true,
            Message: "Telefon numarası doğrulandı ve müşteri kaydı oluşturuldu.",
            CustomerId: customer.Id
        ));
    }

    private static string GenerateRandomEmail()
    {
        string chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        int nameLength = random.Next(6, 12);
        var localPart = new string(Enumerable.Repeat(chars, nameLength)
            .Select(s => s[random.Next(s.Length)]).ToArray());

        return $"{localPart}@generated.local";
    }
}
