using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using PhoneNumbers;
using Shared.Application;
using Shared.Domain;
using Shared.Infrastructure.External.Sms;

namespace Customers.Application.Customers.CreateCustomerAndValidatePhone;

internal sealed class CreateCustomerAndValidatePhoneCommandHandler(
    ICustomersDbContext context,
    ISmsManager smsManager,
    AppSettings appSettings
) : IRequestHandler<CreateCustomerAndValidatePhoneCommand, Result<CreateCustomerAndValidatePhoneResponse>>
{
    public async Task<Result<CreateCustomerAndValidatePhoneResponse>> Handle(
        CreateCustomerAndValidatePhoneCommand request,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Phone))
        {
            return Result.Success(new CreateCustomerAndValidatePhoneResponse(
                Success: false,
                Message: "Telefon numarası boş olamaz.",
                CustomerId: null
            ));
        }

        if (string.IsNullOrWhiteSpace(request.Name))
        {
            return Result.Success(new CreateCustomerAndValidatePhoneResponse(
                Success: false,
                Message: "Ad boş olamaz.",
                CustomerId: null
            ));
        }
        var phoneUtil = PhoneNumberUtil.GetInstance();
        var finalNormalized = "";
        var finalPrefix = "";
        try
        {
            var parsedNumber = phoneUtil.Parse(request.Phone, appSettings.DefaultRegion);
            finalNormalized = parsedNumber.NationalNumber.ToString();
            finalPrefix = parsedNumber.CountryCode.ToString();
        }
        catch (Exception)
        {
            return Result.Success(new CreateCustomerAndValidatePhoneResponse(
                Success: false,
                Message: "Geçersiz telefon numarası formatı.",
                CustomerId: null
            ));
        }
        var existingCustomer = await context.Customers
            .FirstOrDefaultAsync(c => c.Phone == finalNormalized && c.PhonePrefix == finalPrefix, cancellationToken);

        Customer customer;
        if (existingCustomer != null)
        {
            customer = existingCustomer;
        }
        else
        {
            string email = !string.IsNullOrWhiteSpace(request.Email)
                ? request.Email.Trim()
                : GenerateRandomEmail();

            customer = Customer.Create(
                request.Name,
                request.Surname,
                email,
                finalNormalized,
                finalPrefix,
                CustomerType.Individual);

            context.Customers.Add(customer);
        }
        var verificationCode = GenerateVerificationCode();
        customer.SetPhoneVerificationCode(verificationCode);

        try
        {
            var fullPhone = $"{finalPrefix}{finalNormalized}";
            var message = $"Doğrulama kodunuz: {verificationCode}. Bu kod 5 dakika geçerlidir.";
            await smsManager.SendSmsAsync(fullPhone, message);
            await context.SaveChangesAsync(cancellationToken);
            return Result.Success(new CreateCustomerAndValidatePhoneResponse(
                Success: true,
                Message: "Doğrulama kodu gönderildi.",
                CustomerId: customer.Id
            ));
        }
        catch (Exception)
        {
            return Result.Success(new CreateCustomerAndValidatePhoneResponse(
                Success: false,
                Message: "SMS gönderilirken hata oluştu.",
                CustomerId: null
            ));
        }
    }

    private static string GenerateVerificationCode()
    {
        var random = new Random();
        return random.Next(100000, 999999).ToString();
    }

    private static string GenerateRandomEmail()
    {
        string chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        int nameLength = random.Next(6, 12);
        var localPart = new string(Enumerable.Repeat(chars, nameLength)
            .Select(s => s[random.Next(s.Length)]).ToArray());

        return $"{localPart}@generated.local";
    }
}
