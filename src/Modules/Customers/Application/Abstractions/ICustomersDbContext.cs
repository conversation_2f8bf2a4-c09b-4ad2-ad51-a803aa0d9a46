using Customers.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Shared.Infrastructure.Data;

namespace Customers.Application.Abstractions;

public interface ICustomersDbContext : IBaseDbContext
{
    DatabaseFacade Database { get; }
    DbSet<Customer> Customers { get; }
    DbSet<TempCustomer> TempCustomers { get; }
    DbSet<Contact> Contacts { get; }
    DbSet<AdvisorHistory> AdvisorHistory { get; }
    DbSet<Address> Address { get; }
    DbSet<CustomerSource> CustomerSource { get; }
    DbSet<Classification> Classification { get; }
    DbSet<CustomerClassification> CustomerClassification { get; }
    DbSet<Profession> Profession { get; }
    DbSet<Sector> Sector { get; }
    DbSet<NotificationWay> NotificationWays { get; }
    DbSet<PhoneVerification> PhoneVerifications { get; }
}
