﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;

namespace Users.Infrastructure.Authorization;

internal sealed class PermissionAuthorizationPolicyProvider(
    IOptions<AuthorizationOptions> options
) : DefaultAuthorizationPolicyProvider(options)
{
    private readonly AuthorizationOptions _authorizationOptions = options.Value;
    private readonly object _lock = new();

    public override async Task<AuthorizationPolicy?> GetPolicyAsync(string policyName)
    {
        AuthorizationPolicy? policy = await base.GetPolicyAsync(policyName);

        if (policy is not null)
        {
            return policy;
        }
        lock (_lock)
        {
            // Double-check pattern: Lock içinde tekrar kontrol et
            if (_authorizationOptions.GetPolicy(policyName) is AuthorizationPolicy existingPolicy)
            {
                return existingPolicy;
            }

            AuthorizationPolicy permissionPolicy = new AuthorizationPolicyBuilder()
                .AddRequirements(new PermissionRequirement(policyName))
                .Build();

            _authorizationOptions.AddPolicy(policyName, permissionPolicy);

            return permissionPolicy;
        }
    }
}
