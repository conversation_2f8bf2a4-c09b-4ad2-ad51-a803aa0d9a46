using System.Security.Claims;
using Shared.Application;
using Shared.Contracts;
using Shared.Domain;

namespace Host.Infrastructure;

public class WorkContext(
    IHttpContextAccessor httpContextAccessor
) : IWorkContext
{
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    public bool IsAuthenticated => _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

    public Guid UserId
    {
        get
        {
            var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return userIdClaim != null ? Guid.Parse(userIdClaim) : Guid.Empty;
        }
    }

    public async Task<CurrentUser> GetUserAsync()
    {
        if (!IsAuthenticated)
        {
            return null;
        }
        var user = _httpContextAccessor.HttpContext?.User;
        var currentUser = new CurrentUser
        {
            Id = UserId,
            Email = user?.FindFirst(ClaimTypes.Email)?.Value,
            Name = user?.FindFirst(ClaimTypes.Name)?.Value,
            Surname = user?.FindFirst(ClaimTypes.Surname)?.Value,
            Fullname = user?.FindFirst("FullName")?.Value,
            PhoneNumber = user?.FindFirst(ClaimTypes.MobilePhone)?.Value,
            Roles = user?.FindAll(ClaimTypes.Role)?.Select(r => r.Value).ToArray() ?? [],
            RoleIds = (user?.FindAll("RoleId")?.Select(r => r.Value)?.ToArray() ?? []).Where(x => !string.IsNullOrEmpty(x)).Select(x => Guid.Parse(x)).ToArray(),
            InsertDate = user?.FindFirst("InsertDate")?.Value != null ? DateTime.Parse(user?.FindFirst("InsertDate")?.Value ?? "") : null,
        };
        return await Task.FromResult(currentUser);
    }

    public bool HasRole(string role)
    {
        if (!IsAuthenticated)
        {
            return false;
        }
        var user = _httpContextAccessor.HttpContext?.User;
        if (user?.FindAll(ClaimTypes.Role)?.Any(r => r.Value == role) == true)
        {
            return true;
        }
        if (user?.FindAll("RoleNormalizedName")?.Any(r => r.Value == role) == true)
        {
            return true;
        }
        return false;
    }

    public bool HasRole(Guid roleId)
    {
        if (!IsAuthenticated)
        {
            return false;
        }
        var user = _httpContextAccessor.HttpContext?.User;
        return user?.FindAll("RoleId")?.Any(r => r.Value == roleId.ToString()) ?? false;
    }
}
